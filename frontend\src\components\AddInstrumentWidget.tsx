import { useAuth } from "./watchlist/hooks/useAuth";

const isIFrame = window.self !== window.top;
interface AddInstrumentWidgetProps {
  instrumentId: string;
}

const AddInstrumentWidget = ({
  instrumentId,
}: AddInstrumentWidgetProps) => {
  const auth = useAuth();
  if (!auth.isAuthenticated) {
    return null;
  }

  return <button onClick={() => {
    const component = window.euroland.components.WatchlistAddInstrument({
      onChange: () => console.log('changed'),
      instrumentId: instrumentId
    });

    const integrationLayoutPosition = window.xprops
    ? window.xprops.layout.middle
    : "#middleLayout";

  if (isIFrame) {
    component.renderTo(window.parent, integrationLayoutPosition);
  } else {
    let middle = document.getElementById("middleLayout");
    if (!middle) {
      middle = document.createElement("div");
      middle.id = "middleLayout";
      document.body.appendChild(middle);
    }
    component.renderTo(window.parent, integrationLayoutPosition);
  }
  }}>Add to watchlist</button>
};

export default AddInstrumentWidget;
