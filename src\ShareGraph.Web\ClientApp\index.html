<!DOCTYPE html>
<html>

<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta charset="UTF-8">
  <script type="module" src="assets/chartiq/key.js">
  </script>
  <!-- <script type="module" src="assets/promode/app-context-widget/dist/euroland-app-context-widget.umd.js"></script> -->
  <!-- <script type="module" src="assets/promode/auth-widget/dist/auth-widget.umd.js"></script> -->
  <!-- <script type="module" src="assets/promode/watchlist-widget/dist/watchlist-widget.umd.js"></script> -->
  <!-- <script type="module" src="assets/promode/watchlist-widget/dist/watchlist-widget.umd.js"></script> -->
  
  <!-- <script type="module" src="assets/promode/share-alert-widget/dist/share-alert-widget.umd.js"></script> -->
  <script type="module" src="assets/widget/auth-widget.umd.js"></script>
  <script src="assets/widget/euroland-app-context-widget.umd.js"></script>
  <script src="http://localhost:4173/watchlist-widget.umd.js" defer></script>
  <script src="http://localhost:4174/news-widget.umd.js"></script>

</head>

<body>
  <euroland-auth></euroland-auth>
  <euroland-app-context-widget></euroland-app-context-widget>
  <div data-xsrf-token="%XSRF_TOKEN%" id="app" class='app'></div>
  <euroland-auth></euroland-auth>
  <euroland-realtime-notification position="bottom-right"></euroland-realtime-notification>
  <euroland-watchlist-root></euroland-watchlist-root>
</body>

</html>