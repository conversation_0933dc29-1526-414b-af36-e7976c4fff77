Write-Host "Checking if Corepack is installed..."

# Check if corepack command exists
$corepackExists = $null -ne (Get-Command "corepack" -ErrorAction SilentlyContinue)

if (-not $corepackExists) {
    Write-Host "Corepack is not installed. Attempting to install..."
    
    # Check if Node.js is installed
    $nodeExists = $null -ne (Get-Command "node" -ErrorAction SilentlyContinue)
    if (-not $nodeExists) {
        Write-Host "Error: Node.js is not installed. Please install Node.js first." -ForegroundColor Red
        exit 1
    }
    
    # Get Node.js version
    $NODE_VERSION = node -v
    Write-Host "Node.js $NODE_VERSION detected."
    
    # Install corepack
    Write-Host "Installing Corepack..."
    npm install -g corepack
    
    # Verify installation
    $corepackExists = $null -ne (Get-Command "corepack" -ErrorAction SilentlyContinue)
    if (-not $corepackExists) {
        Write-Host "Error: Failed to install Corepack." -ForegroundColor Red
        exit 1
    } else {
        Write-Host "Corepack has been installed successfully." -ForegroundColor Green
    }
} else {
    # Get corepack version
    $COREPACK_VERSION = corepack --version
    Write-Host "Corepack version $COREPACK_VERSION is already installed."
}

# Enable corepack
Write-Host "Enabling Corepack..."
corepack enable

Write-Host "Corepack setup complete." -ForegroundColor Green
exit 0