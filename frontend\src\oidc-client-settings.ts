import { OidcClientSettings } from "oidc-client-ts";
import { OidcAuthAppRoutes } from "./auth/core";

const OidcClientSettingsPromise = new Promise<OidcClientSettings>((resolve) => {
  let baseUrl = (document.querySelector('base') || {} as HTMLBaseElement).href;

  if(/\/$/.test(baseUrl)) {
    baseUrl = baseUrl.replace(/\/$/, '');
  }

  const config = {
    authority: import.meta.env.VITE_IDENTITY_URL,
    client_id: import.meta.env.VITE_IDENTITY_CLIENT_ID,
    redirect_uri: `${baseUrl}${OidcAuthAppRoutes.SIGNIN_CALLBACK}`,
    silent_redirect_uri: `${baseUrl}${OidcAuthAppRoutes.SIGNIN_CALLBACK}`,
    post_logout_redirect_uri: `${baseUrl}${OidcAuthAppRoutes.SIGNOUT_CALLBACK}`,
    response_type: 'code',
    loadUserInfo: true,
    scope: 'openid profile'
  };

  resolve(config);
});

export default () => OidcClientSettingsPromise;
