<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Nordea - Investors</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: Arial, sans-serif;
      }

      /* Notification styles */
      .notifications-container {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 300px;
      }

      .notification-bubble {
        background: white;
        border-radius: 8px;
        padding: 12px 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        border-left: 4px solid #0000cd;
        animation-duration: 0.3s !important;
      }

      .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .notification-icon {
        color: #0000cd;
        font-size: 1.2rem;
      }

      .notification-text {
        color: #333;
        font-size: 0.9rem;
        font-weight: 500;
      }

      .notification-close {
        color: #666;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;
      }

      .notification-close:hover {
        background: #f0f0f0;
        color: #333;
      }

      .positive {
        border-left-color: #00c851;
      }

      .positive .notification-icon {
        color: #00c851;
      }

      .neutral {
        border-left-color: #0000cd;
      }

      .neutral .notification-icon {
        color: #0000cd;
      }

      /* Header styles */
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        border-bottom: 1px solid #eee;
        background: white;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .logo svg {
        height: 29px;
        fill: #0000cd;
        transition: fill 0.3s ease;
      }

      .logo:hover svg {
        fill: #000080;
      }

      .nav {
        display: flex;
        gap: 2rem;
      }

      .nav a {
        text-decoration: none;
        color: #333;
        font-size: 1rem;
        position: relative;
        padding: 0.5rem 0;
      }

      .nav a::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background: #0000cd;
        transition: width 0.3s ease;
      }

      .nav a:hover::after {
        width: 100%;
      }

      .search-container {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 0.5rem;
        position: relative;
        transition: all 0.3s ease;
      }

      .search-container:focus-within {
        box-shadow: 0 0 0 2px #0000cd;
      }

      .search-input {
        border: none;
        outline: none;
        padding: 0.25rem;
      }

      .header-buttons {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .icon-button {
        border: none;
        background: none;
        cursor: pointer;
        padding: 0.5rem;
        color: #666;
        transition: color 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .icon-button:hover {
        color: #0000cd;
      }

      .icon-button svg {
        width: 16px;
        height: 16px;
        transition: all 0.3s ease;
      }

      .icon-button:hover svg {
        transform: scale(1.1);
      }

      .icon-button svg path {
        fill: currentColor;
        stroke: currentColor;
      }

      /* Main content styles */
      .main-content {
        display: grid;
        grid-template-columns: 250px 1fr;
        gap: 2rem;
        padding: 2rem;
      }

      /* Sidebar styles */
      .sidebar {
        background-color: #fff5f5;
        padding: 2rem;
        border-radius: 8px;
      }

      .sidebar h2 {
        color: #000080;
        margin-bottom: 1rem;
      }

      .sidebar-links {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .sidebar-links a {
        text-decoration: none;
        color: #000080;
      }

      /* News section styles */
      .news-section {
        padding: 1rem;
      }

      .news-section h1 {
        color: #000080;
        margin-bottom: 2rem;
        font-size: 2.5rem;
      }

      .news-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
      }

      .news-card {
        border: 1px solid #eee;
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .news-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .news-card img {
        width: 100%;
        height: 300px;
        object-fit: cover;
      }

      .news-content {
        padding: 1.5rem;
      }

      .news-content h3 {
        color: #000080;
        margin-bottom: 1rem;
        font-size: 1.5rem;
      }

      .news-content p {
        color: #666;
        line-height: 1.6;
      }

      /* Hero image */
      .hero-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        margin-bottom: 2rem;
        position: relative;
      }

      .hero-image::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.4));
      }

      /* Responsive design */
      @media (max-width: 1024px) {
        .main-content {
          grid-template-columns: 1fr;
        }

        .news-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (max-width: 768px) {
        .nav {
          display: none;
        }

        .header {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <a href="#" class="logo">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 136 29"
          width="136"
          height="29"
          fill="inherit"
          role="img"
          aria-labelledby="a11y-nav-logo-title-mob"
        >
          <!-- ... SVG content ... -->
        </svg>
      </a>
      <nav class="nav">
        <a href="#">About us</a>
        <a href="#">Investors</a>
        <a href="#">News & insights</a>
        <a href="#">Careers</a>
        <a href="#">Sustainability</a>
        <a href="#">Our services</a>
      </nav>
      <div class="header-buttons">
        <div class="search-container">
          <input type="text" placeholder="Search" class="search-input" />
        </div>
        <button class="icon-button" onclick="handleLogin()">
          <i class="fas fa-user"></i>
        </button>
      </div>
    </header>

    <!-- Add notifications container -->
    <div class="notifications-container"></div>

    <img
      src="https://www.nordea.com/sites/default/files/styles/hero_big/public/2021-04/nodea-investor-relations1.jpg.webp?itok=kOOI4oo4"
      alt="Nordea building"
      class="hero-image"
    />

    <main class="main-content">
      <aside class="sidebar">
        <h2>Most read pages</h2>
        <div class="sidebar-links">
          <a href="#">Our investment case</a>
          <a href="#">Latest interim results</a>
          <a href="#">Latest annual report</a>
          <a href="#">Investor news</a>
          <a href="#">Financial calendar</a>
          <a href="#">Dividend</a>
          <a href="#">Contact Investor Relations</a>
        </div>
      </aside>

      <section class="news-section">
        <h1>News</h1>
        <div class="news-grid">
          <article class="news-card">
            <img
              src="https://www.nordea.com/sites/default/files/styles/full_image/public/2024-11/ian-smith-.jpg.webp?itok=rr6PAw6v"
              alt="CFO Ian Smith"
            />
            <div class="news-content">
              <h3>
                Nordea's CFO Ian Smith named Chief Financial Officer of the year
              </h3>
              <p>
                Ian Smith, CFO of Nordea since 2020, has today been named the
                2024 Chief Financial Officer of the Year in Finland in the
                annual awards event organised by Finnish Talouselämä and
                Accenture Finland.
              </p>
            </div>
          </article>
          <article class="news-card">
            <img
              src="https://www.nordea.com/sites/default/files/styles/article_teaser/public/2025-01/q4-2024-main-image-1920.jpg.webp?itok=1sNYXAxg"
              alt="Fourth quarter results"
            />
            <div class="news-content">
              <h3>Fourth-quarter results 2024</h3>
              <p>
                We continued to perform well in the fourth quarter.
                Profitability was again at a good level, and our return on
                equity has clearly exceeded 15% for the past eight quarters.
              </p>
            </div>
          </article>
        </div>
      </section>
    </main>

    <script>
      // Search functionality
      function handleSearch() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();
        if (searchTerm) {
          alert(`Searching for: ${searchTerm}`);
          searchInput.value = "";
        }
      }

      // Login functionality
      function handleLogin() {
        alert("Login functionality coming soon!");
      }

      // Add hover effects to navigation links
      document.querySelectorAll(".nav a").forEach((link) => {
        link.addEventListener("mouseenter", () => {
          link.style.color = "#0000CD";
        });
        link.addEventListener("mouseleave", () => {
          link.style.color = "#333";
        });
      });

      // Add hover effects to sidebar links
      document.querySelectorAll(".sidebar-links a").forEach((link) => {
        link.addEventListener("mouseenter", () => {
          link.style.textDecoration = "underline";
        });
        link.addEventListener("mouseleave", () => {
          link.style.textDecoration = "none";
        });
      });

      // Smooth scroll for navigation links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          document.querySelector(this.getAttribute("href")).scrollIntoView({
            behavior: "smooth",
          });
        });
      });

      // Add loading animation for images
      document.querySelectorAll("img").forEach((img) => {
        img.addEventListener("load", function () {
          this.style.opacity = "1";
        });
        img.style.opacity = "0";
        img.style.transition = "opacity 0.3s ease";
      });
    </script>

    <script>
      (function (w, d, s, e, l, i) {
        w[s] = w[s] || [];
        w[s].push({ company: e });
        w[s].push({ reg_key: l });
        w[s].push({ svc_url: i });
        var f = d.getElementsByTagName("script")[0],
          j = d.createElement("script");
        j.async = true;
        j.src = "https://dev.vn.euroland.com/tools/integration/latest/embed.js";
        j.id = "euroland-integration";
        !d.getElementById(j.id) && f.parentNode.insertBefore(j, f);
      })(
        window,
        document,
        "clientCfg",
        "s-volv",
        null,
        "http://localhost:3005"
      );
    </script>

    <script>
      function createAISearchApp() {
        if (window.euroland) {
          var container = document.createElement("div");
          document.body.appendChild(container);
          // Programatic Integration guide:
          // https://dev.vn.euroland.com/tools/integration/latest/integration-guide.html#programmatic-integration
          window.euroland.create({
            absoluteToolUrl: location.origin,
            container: container, // DOM element to render the tool
            name: "aisearch", // Tool name (from supported tools list)
            company: "s-volv", // Company identifier (ID or code depending on tool),
            language: "en-gb",
          });
        }
      }
      window.addEventListener("load", createAISearchApp);
    </script>
  </body>
</html>
