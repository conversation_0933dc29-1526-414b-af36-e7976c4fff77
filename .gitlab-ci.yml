stages:
  - build
  - quality
  - release
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  GIT_STRATEGY: fetch
  DOTNET_VERSION: "8.0"
  API_DIR: "backend"
  CLIENT_DIR: "frontend"

######### Shared variables #########
.deploy_template: &deploy_template
  script:
    - |
      # Copy all client files and subdirectories to wwwroot/

      robocopy "$env:CLIENT_DIR/dist/" "$env:API_DIR/publish/wwwroot/" /MOV /MIR /E

      # Copy environment appSettings.{ENVIRONMENT}.json if exists
      $envAppSetting = "appSettings.$env:ENVIRONMENT.json"
      if (Test-Path "$env:API_DIR/src/AppSettings/$envAppSetting") {
          robocopy "$env:API_DIR/src/AppSettings/" "$env:API_DIR/publish/" $envAppSetting /MOV
      } else {
          Write-Output "File '$envAppSetting' does not exist."
      }

      $webconfigFile = "$env:API_DIR/publish/web.config"
      $envName = "$env:ENVIRONMENT"

      # Read file content
      $content = Get-Content -Path $webconfigFile -Raw

      # Replace the placeholder string
      $content = $content -replace "ENVIRONMENT_NAME", $envName

      # Write back to the file
      Set-Content -Path $webconfigFile -Value $content

      Write-Output "ENVIRONMENT to $env:ENVIRONMENT."
    - |
      $deployParams = @{
          source      = Join-Path $PWD "$env:API_DIR/publish"
          siteName   = "$env:SITE_NAME"
          appPath    = "$env:APP_PATH"
          user       = "$DEPLOY_USER"
          passwd     = "$DEPLOY_PWD"
          server     = "$env:DEPLOY_SERVER1"
          port       = $env:DEPLOY_SERVER_PORT1
      }

      & ".\ms_deploy.ps1" @deployParams

      if($env:DEPLOY_SERVER2 -and $env:DEPLOY_SERVER_PORT2) {
        $deployParams.server = $env:DEPLOY_SERVER2
        $deployParams.port = $env:DEPLOY_SERVER_PORT2

        & ".\ms_deploy.ps1" @deployParams
      }

      if ($LASTEXITCODE -ne 0) {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
      }

.build_template: &build_template
  script:
    - cd $API_DIR
    - dotnet restore
    - dotnet publish --no-restore -c Release -p:EnvironmentName="ENVIRONMENT_NAME" -o "publish"

client-build-qa:
  tags:
    - vn-v-docker
  image: node:20.19.0
  stage: build
  script:
    - cd frontend
    - npm install
    - npm run build "--" --mode qa
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always  # Run always on develop branch commits
  artifacts:
    paths:
      - ${CLIENT_DIR}/dist/
    untracked: false
    name: "client-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"
    expire_in: 2 hours

api-build-qa:
  tags:
    - vn-v-docker
  stage: build
  image: mcr.microsoft.com/dotnet/sdk:${DOTNET_VERSION}
  <<: *build_template
  rules:
    - if: $CI_MERGE_REQUEST_TITLE =~ /^wip[:]/i
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
      when: always
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always  # Run always on develop branch commits
  artifacts:
    paths:
      - ${API_DIR}/publish/
    untracked: false
    name: "api-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"
    expire_in: 2 hours

deploy-qa:
  stage: deploy
  tags:
    - vietnam-buildtest-powershell
  dependencies:
    - client-build-qa
    - api-build-qa
  variables:
    ENVIRONMENT: 'QA'
    DEPLOY_SERVER1: 'BINGO'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:DEV_VN_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:DEV_VN_DEPLOY_PSW"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: always

# Production jobs
client-build-production:
  tags:
    - ee-v-docker1-dind
  image: node:20
  stage: build
  script:
    - cd frontend
    - npm install
    - npm run build "--" --mode production
  rules:
    - if: $CI_COMMIT_BRANCH == "next"
      when: always
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
  artifacts:
    paths:
      - ${CLIENT_DIR}/dist/
    untracked: false
    name: "client-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"

api-build-production:
  tags:
    - ee-v-docker1-dind
  stage: build
  image: mcr.microsoft.com/dotnet/sdk:${DOTNET_VERSION}
  <<: *build_template
  rules:
    - if: $CI_COMMIT_BRANCH == "next"
      when: always
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
  artifacts:
    paths:
      - ${API_DIR}/publish/
    untracked: false
    name: "api-$CI_JOB_STAGE-$CI_COMMIT_REF_NAME"

deploy-gamma:
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'Gamma'
    DEPLOY_SERVER1: 'ee-v-gamma1.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: gamma
    url: 'https://gamma.euroland.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:GAMMA_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:GAMMA_DEPLOY_PSW"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "next"
      when: always

deploy-staging:
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'Staging'
    DEPLOY_SERVER1: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'staging-site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: staging
    url: 'https://staging-gr.eurolandir.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ee-v-webcat15,16":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'Production'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'production-site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: ground
    url: 'https://gr-web-ws1.eurolandir.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ne-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'CloudNE'
    DEPLOY_SERVER1: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: cloudne
    url: 'https://ne-web-ws1.eurolandir.com.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ea-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'CloudEA'
    DEPLOY_SERVER1: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: cloudea
    url: 'https://ea-web-ws1.eurolandir.com.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"uae-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'CloudUAE'
    DEPLOY_SERVER1: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: clouduae
    url: 'https://uae-web-ws1.eurolandir.com.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"ksa-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'CloudKSA'
    DEPLOY_SERVER1: '**************'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: '**************'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: cloudksa
    url: 'https://ksa-web-ws1.eurolandir.com.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual

"cn-web-ws1,2":
  stage: deploy
  tags:
    - ee-buildtest-powershell
  dependencies:
    - client-build-production
    - api-build-production
  variables:
    ENVIRONMENT: 'CloudCN'
    DEPLOY_SERVER1: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/aisearch'
  environment:
    name: cloudcn
    url: 'https://ksa-web-ws1.eurolandir.com.com/tools/aisearch'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  <<: *deploy_template
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
