import { AISearchWidget } from "@/components/AISearchWidget";
import { XProps } from "./zoid";
import AuthControl from "./controls/AuthControl";
import ConfirmPopupControl from "./controls/ConfirmPopupControl";
import SearchWidgetControl from "./controls/SearchWidgetControl";
import { useEffect, useState } from "react";
import { apiClient } from "./services/clientInstance";
import {
  API_PATHS,
  ICompanyInfo,
  ICompanyInfoResponse,
} from "./services/apiEndpoints";
import { LayoutPosition } from "./config/interface";
import { appSettings } from "./config/appSettings";
import {
  getAISearchSettings,
  AISearchSettings,
} from "./services/getAISearchSettings";

function App() {
  const [companyInfo, setCompanyInfo] = useState<ICompanyInfo | null>(null);
  const [aiSearchSettings, setAISearchSettings] =
    useState<AISearchSettings | null>(null);

  useEffect(() => {
    const abortController = new AbortController();

    Promise.all([
      apiClient.instance.get<ICompanyInfoResponse>(API_PATHS.COMPANY_INFO, undefined, {
        signal: abortController.signal,
      }),
      getAISearchSettings(appSettings.language, abortController.signal),
    ])
      .then(([companyInfoResponse, settings]) => {
        const companyData = companyInfoResponse.data;
        const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const companyTimezone = companyData.useLocalTimezone ? browserTimezone : (companyData.timezone ?? browserTimezone);
        setCompanyInfo({
          companyName: companyData.companyName.name,
          logo: companyData.companyLogo || "",
          companyCode: companyData.companyCode,
          primaryColor: companyData.primaryColor,
          industry: companyData.industry || "",
          timezone: companyTimezone,
        });

        if(!settings.placeholders) {
          settings.placeholders = companyData.companyName.name
        }

        setAISearchSettings(settings);

        // Apply CSS settings after settings are fetched
        window.xprops.css({
          position: "fixed",
          [settings.widgetBehavior === LayoutPosition.LEFT ? "left" : "right"]:
            "20px", // Position based on RTL/LTR
          bottom: "20px",
          width: "215px",
          "z-index": "200000", // Keep staying in front of search window
          overflowX: "hidden", // Important to make animating when hover the search button
        });
      })
      .catch((error) => {
        // Handle potential errors, e.g., log them or show a user message
        // Check if it's an abort error before logging/showing
        if (error.name !== 'AbortError') {
           console.error("Failed to fetch initial data:", error);
        }
      });


    return () => {
      abortController.abort();
    };
  }, []);

  if (!companyInfo) return null;
  if (!aiSearchSettings) return null;

  const position = aiSearchSettings.widgetBehavior;

  return (
    <>
      <AISearchWidget
        {...(window.xprops as unknown as XProps)}
        companyInfo={companyInfo}
        aiSearchSettings={aiSearchSettings}
        position={position}
      />
      <AuthControl/>
      <ConfirmPopupControl aiSearchSettings={aiSearchSettings}/>
      <SearchWidgetControl
        companyInfo={companyInfo}
        position={position}
        aiSearchSettings={aiSearchSettings}
      />
    </>
  );
}

export default App;
