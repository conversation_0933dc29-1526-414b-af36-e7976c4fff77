 

import {XProps, ZoidComponentOptions, Euroland} from "./zoid";

export interface IZComponentOptions extends ZoidComponentOptions {
  template?: {
    name: 'modal' | 'popup' | 'dynamic',
    clickOverlayToClose?: boolean,
    hideCloseButton?: boolean,
    ignoreBackdropClick?: boolean,
    customStyles?: string,
    backdropBgColor?: string,
    backdrop?: boolean,
    autoResize?: boolean,
    styles?: any,
  }
};

export type IZComponentCreator = (props: {
  onClose?: () => void;
  onRendered?: () => void;
   
  [key: string]: any;
}) => IntegrationComponentInstance

 
export type IntegrationComponent = (props: any) => IntegrationComponentInstance

type IZComponentInstance = {
  renderTo: (w: Window, layout: string, type?: 'popup' | 'iframe') => void,
}

declare global {
  interface Window {
    euroland: Euroland;
    xprops: XProps;
  }
}

export {}

// Search result
export interface ISearchHistory {
  query: string;
  result: string;
  timestamp: string;
}
