using System;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Types.Timezone;
using Euroland.FlipIT.SData.API.Infrastructure.DataCache;
using Euroland.FlipIT.SData.API.Infrastructure.Factories;
using Euroland.FlipIT.Shared.CachingManager;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Euroland.FlipIT.SData.API.GraphQL.Services;

public class MarketService : IMarketService
{
  private readonly ISharkDbContextAbstractFactory _factory;
  private readonly IGraphQLExecutionContext _queryRequestContext;
  private readonly IConfigurableCacheManager _configurableCacheManager;

  public MarketService(ISharkDbContextAbstractFactory factory, IGraphQLExecutionContext queryRequestContext,
    IConfigurableCacheManager configurableCacheManager)
  {
    _factory = factory ?? throw new ArgumentNullException(nameof(factory));
    _queryRequestContext = queryRequestContext ?? throw new ArgumentNullException(nameof(queryRequestContext));
    _configurableCacheManager =
      configurableCacheManager ?? throw new ArgumentNullException(nameof(configurableCacheManager));
  }

  public async Task<MarketDto?> GetMarketById(int marketId)
  {
    var marketFromCache = _configurableCacheManager.GetCache<MarketDto, string>(
      CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketDto, int>(marketId.ToString(), m => m.Id));

    if (marketFromCache != null)
    {
      marketFromCache.TimezoneName = WindowsTimezoneToIANA.Convert(marketFromCache.TimezoneName, null, null);
      return marketFromCache;
    }

    await using var sharkDbContext =
      await _factory.CreateDbContextAsync(_queryRequestContext.UseRealtime, _queryRequestContext.UseCloud);

    var market = await sharkDbContext.Market
      .AsNoTracking()
      .Where(m => m.MarketNumber == marketId)
      .WithAutoMapper().ToProjection<MarketDto>().FirstOrDefaultAsync();

    if (market == null) return market;

    _configurableCacheManager.SetCache(
      market,
      CacheKeyHelper.GenerateKeyByPropOfDtoObject<MarketDto, int>(market.Id.ToString(), m => m.Id)
    );

    market.TimezoneName = WindowsTimezoneToIANA.Convert(market.TimezoneName, null, null);

    return market;
  }
}
