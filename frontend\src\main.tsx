import { createRoot } from "react-dom/client";
import { <PERSON>idc<PERSON>uth<PERSON><PERSON>ider, OidcAuth<PERSON>pp, isOidcAuthOpened } from "./auth";
import OidcClientSettingsPromise from "./oidc-client-settings";
import { updateTranslation } from "./lib/utils";
import { appSettings } from "./config/appSettings.ts";
import "./styles/index.scss";
import { UserManager } from "oidc-client-ts";
import { ApiClient } from "./services/client.ts";
import { apiClient, apiUserPreferenceClient } from "./services/clientInstance.ts";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import {ZoidSearchWidget} from "./components/SearchWidget";
import {lazy} from "react";
import integrateTool from "./integrateTool.ts";
import { ZoidPopUpConfirm } from "./components/PopUpConfirm";
import { ZoidPopUpOpenExternalLinkConfirm } from "./components/PopUpOpenExternalLinkConfirm";
import {basePath} from "./lib/base-url.ts";
import {AUTH_ENABLED} from "./helpers/constants.ts";

(async () => {
  const root = createRoot(document.getElementById("root")!);
  const [oidcClientSettings] = await Promise.all([
    OidcClientSettingsPromise(),
    updateTranslation(appSettings.language)
  ]);


  const userManager = new UserManager(oidcClientSettings);
  apiClient.instance = new ApiClient(() =>
    userManager.getUser().then((user) => user?.access_token)
  );
  apiUserPreferenceClient.instance = new ApiClient(() =>
    userManager.getUser().then((user) => user?.access_token),
    import.meta.env.VITE_API_USER_PREFERENCES_URL
  );

  document.documentElement.dir = appSettings.language === 'ar-ae' ? 'rtl' : 'ltr';

  const App = lazy(() => integrateTool().then(() => import('./App.tsx')));

  root.render(
    <OidcAuthProvider
      userManagerProp={userManager}
      clientSettings={oidcClientSettings}
      enabled={AUTH_ENABLED}
    >
      {isOidcAuthOpened()
        ? <OidcAuthApp baseUrl={basePath} />
        : <BrowserRouter basename={basePath}>
            <Routes>
              <Route path="/" element={<App />} />
              <Route path={ZoidSearchWidget.path} element={<ZoidSearchWidget.ComponentWrapper />} />
              <Route path={ZoidPopUpConfirm.path} element={<ZoidPopUpConfirm.ComponentWrapper />} />
              <Route path={ZoidPopUpOpenExternalLinkConfirm.path} element={<ZoidPopUpOpenExternalLinkConfirm.ComponentWrapper />} />
            </Routes>
          </BrowserRouter>}
    </OidcAuthProvider>
  );
})();
