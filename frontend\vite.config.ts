import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
//import basicSsl from '@vitejs/plugin-basic-ssl';
import path from 'path';

// https://vite.dev/config/
export default ({ mode }: { mode: string }) => {
  // Load app-level env vars to node-level env vars.
  process.env = {...process.env, ...loadEnv(mode, process.cwd())};

  const BASE_URL = process.env.VITE_BASE_URL || '/';

  return defineConfig({
    base: BASE_URL,
    define: {
      'import.meta.env.VITE_BUILD_TIME': JSON.stringify(new Date().getTime())
    },
    plugins: [react()/*, basicSsl()*/],
    server: {
      port: 3005,
      open: 'nordea.html',
      strictPort: true, // Force to exit if port is already in use.
      proxy: {
        '/graphql': {
          target: 'https://gamma.euroland.com/tools/sdata-api/graphql',
          changeOrigin: true,
          secure: true,
        }
      }
    },
    css: {
      devSourcemap: true,
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  });
}
