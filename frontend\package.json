{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "test": "vitest", "preview": "vite preview"}, "dependencies": {"@euroland/libs": "^2.1.2", "@euroland/react-tablist": "^0.0.6", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@number-flow/react": "^0.5.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "es-toolkit": "^1.31.0", "frontend": "file:", "immer": "^10.1.1", "lucide-react": "^0.471.0", "next-themes": "^0.4.4", "oidc-client-ts": "^3.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.3", "react-router-dom": "^7.1.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@krakenjs/zoid": "^10.4.0", "@types/node": "^22.10.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "sass": "^1.83.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vitest": "^2.1.8"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}