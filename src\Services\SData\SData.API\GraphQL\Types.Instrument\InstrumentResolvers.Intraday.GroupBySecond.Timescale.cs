using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Euroland.FlipIT.SData.API.Dto;
using Euroland.FlipIT.SData.API.GraphQL.RequestContext;
using Euroland.FlipIT.SData.API.GraphQL.Services;
using Euroland.FlipIT.SData.API.Infrastructure;
using Euroland.FlipIT.SData.API.Infrastructure.Entities;
using Euroland.FlipIT.SData.API.Infrastructure.Entities.Timescale;
using HotChocolate;
using HotChocolate.Resolvers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using NpgsqlTypes;

namespace Euroland.FlipIT.SData.API.GraphQL.Types.Instrument;

internal partial class InstrumentResolvers
{
  public async Task<IQueryable<InstrumentHistoryDto>> GetIntradayGroupBySecondTimescale(
    IResolverContext resolverContext,
    [Service] IGraphQLExecutionContext executionContext,
    [Service] IMarketService marketService,
    [Parent] InstrumentDto instrument,
    TimescaleDbContext timescaleDbContext,
    int timeIntervalGrouping
  )
  {
    var hasAdjustedClosePrice = executionContext.UseAdjClose;

    var market = await marketService.GetMarketById(instrument.MarketID);

    if (market == null)
    {
      throw new InvalidOperationException($"Can't get market of instrument with id: {instrument.Id}.");
    }

    if (timeIntervalGrouping < 1)
    {
      throw new ArgumentOutOfRangeException(nameof(timeIntervalGrouping),
        "The time interval must be greater than or equal to 1.");
    }

    var caggTimeInterval = new List<int>() { 1, 3, 5, 10, 15, 30 };

    var dataByPeriod = caggTimeInterval.Contains(timeIntervalGrouping)
      ? GetCAggGroupBySecondQueryTimescale(timescaleDbContext, instrument.Id, timeIntervalGrouping,
          hasAdjustedClosePrice, market.OpenTimeLocal, market.TimezoneName)
        .TagWith("<intraday_log>")
      : GetDailyHistoryGroupBySecondQueryTimescale(timescaleDbContext, instrument.Id, timeIntervalGrouping,
          hasAdjustedClosePrice, market.OpenTimeLocal, market.TimezoneName)
        .TagWith("<intraday_log>");

    var exchangeCurrency = executionContext.ExchangeCurrency;
    var hasExchangeCurrency = !string.IsNullOrEmpty(exchangeCurrency);

    if (!hasExchangeCurrency ||
        string.IsNullOrEmpty(instrument.CurrencyCode))
    {
      return dataByPeriod.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      ).WithAutoMapper().ToProjection<InstrumentHistoryDto>();
    }

    var rateView =
      timescaleDbContext.InstrumentCurrencyPairQueryable($"{instrument.CurrencyCode}{exchangeCurrency.ToUpper()}");

    var query = (
      from price in dataByPeriod
      from rate in rateView.Where(r => r.Date.Date == price.DateTime.Date).DefaultIfEmpty()
      select new { price, rate }
    ).Select(grouping => new InstrumentHistory
    {
      InstrumentId = grouping.price.InstrumentId,
      DateTime = grouping.price.DateTime,
      Close = grouping.price.Close * (grouping.rate != null ? grouping.rate.Rate : 1m),
      High = grouping.price.High * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Open = grouping.price.Open * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Low = grouping.price.Low * (grouping.rate != null ? grouping.rate.Rate : 1m),
      Volume = grouping.price.Volume,
    });

    return query.OrderByArgumentOrDefault(
        resolverContext,
        q => q.OrderByDescending(p => p.DateTime)
      )
      .WithAutoMapper().ToProjection<InstrumentHistoryDto>();
  }

  private IQueryable<InstrumentHistoryTimescale> GetCAggGroupBySecondQueryTimescale(
    TimescaleDbContext timescaleDbContext,
    int instrumentId,
    int groupingTimeIntervalInSecond,
    bool hasAdjustedClosePrice,
    string marketOpenTimeLocal,
    string marketTimezoneName)
  {
    var ohlcvGroupBySecondTable = groupingTimeIntervalInSecond switch
    {
      1 => "ohlcv_group_by_second",
      _ => throw new ArgumentOutOfRangeException(nameof(groupingTimeIntervalInSecond),
        "The time interval must be one of the following: 1 seconds.")
    };

    var query = timescaleDbContext.InstrumentHistoryTimescales
      .FromSqlRaw($@"
               SELECT
                    ohlcv.instrument_id,
                    ohlcv.second_bucket AS second_bucket_grouped,
                    ohlcv.open * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS open,
                    ohlcv.high * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS high,
                    ohlcv.low * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS low,
                    ohlcv.close * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS close,
                    ohlcv.volume AS volume
                FROM
                    {ohlcvGroupBySecondTable} AS ohlcv
                LEFT JOIN LATERAL (
                    SELECT carp.cumulative_adjustment_factor
                    FROM cumulative_adjustments_running_product AS carp
                    WHERE
                        carp.instrument_id = ohlcv.instrument_id
                        AND @adjClose = true
                        AND ohlcv.second_bucket < carp.action_date
                    ORDER BY carp.action_date ASC
                    LIMIT 1
                ) AS ca ON TRUE
                WHERE
                    ohlcv.instrument_id = @instrumentId
      ",
        new NpgsqlParameter("adjClose", NpgsqlDbType.Boolean) { Value = hasAdjustedClosePrice },
        new NpgsqlParameter("instrumentId", NpgsqlDbType.Integer) { Value = instrumentId });
    return query;
  }

  private IQueryable<InstrumentHistoryTimescale> GetDailyHistoryGroupBySecondQueryTimescale(
    TimescaleDbContext timescaleDbContext,
    int instrumentId,
    int groupingTimeIntervalInSecond,
    bool hasAdjustedClosePrice,
    string marketOpenTimeLocal,
    string marketTimezoneName)
  {
    var query = timescaleDbContext.InstrumentHistoryTimescales
      .FromSqlRaw($@"
               SELECT
                  rdh_grouped.instrument_id,
                  rdh_grouped.second_bucket_grouped,
                  rdh_grouped.open_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS open,
                  rdh_grouped.high_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS high,
                  rdh_grouped.low_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS low,
                  rdh_grouped.close_price * COALESCE(ca.cumulative_adjustment_factor, 1.0) AS close,
                  rdh_grouped.volume_traded AS volume
              FROM (
                  SELECT
                      instrument_id,
                      time_bucket(
                        make_interval(secs => @groupingTimeIntervalInSecond),
                        second_bucket,
                        (DATE(second_bucket) + TIME '{marketOpenTimeLocal}') AT TIME ZONE '{marketTimezoneName}'
                      ) AS second_bucket_grouped,
                      FIRST(open, second_bucket) AS open_price,
                      MAX(high) AS high_price,
                      MIN(low) AS low_price,
                      LAST(close, second_bucket) AS close_price,
                      SUM(volume) AS volume_traded
                  FROM ohlcv_group_by_second
                  WHERE instrument_id = @instrumentId
                  GROUP BY instrument_id, second_bucket_grouped
              ) AS rdh_grouped
              LEFT JOIN LATERAL (
                  SELECT carp.cumulative_adjustment_factor
	                FROM cumulative_adjustments_running_product AS carp
	                WHERE carp.instrument_id = rdh_grouped.instrument_id
	                  AND @adjClose = true
	                  AND rdh_grouped.second_bucket_grouped < carp.action_date
	                ORDER BY carp.action_date ASC
	                LIMIT 1
              ) AS ca ON TRUE
      ",
        new NpgsqlParameter("adjClose", NpgsqlDbType.Boolean) { Value = hasAdjustedClosePrice },
        new NpgsqlParameter("instrumentId", NpgsqlDbType.Integer) { Value = instrumentId },
        new NpgsqlParameter("groupingTimeIntervalInSecond", NpgsqlDbType.Integer)
          { Value = groupingTimeIntervalInSecond });
    return query;
  }
}
