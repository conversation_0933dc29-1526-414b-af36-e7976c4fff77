import useChartTitle from '../../../customHooks/useChartTitle';
import ChartShowRange from './ChartShowRange';
import ChartTitle from './ChartTitle';
import useComparisonWrapperStyle from '../../../customHooks/useComparisonWrapperStyle';
import { useSelectedTicker } from '../../../customHooks/useTickers';
import { openAddInstrument } from '../../../customHooks/usePromode';

const ChartHeader = () => {
  const chartTitleData = useChartTitle();
  const { chartHeaderRef } = useComparisonWrapperStyle();

  const handleClickOpenAddInstrument = e => {
    if(e.nativeEvent instanceof CustomEvent) {
      return;
    }
    openAddInstrument();
  };


  return (
    <div className="chart-header" ref={chartHeaderRef}>
      

      <div className="chart-header__title-wrapper">
        <div className="chart-header__title-add-instrument">
          <ChartTitle chartTitleData={chartTitleData} />
          {/* <button onClick={handleClickOpenAddInstrument}>Add Instrument</button> */}
          
          <euroland-add-instrument instrumentId={107277}></euroland-add-instrument>
        </div>
        <div className="chart-header__show-range">
          {/* <AddIntrumentButton instrumentId={selectedTicker.instrumentId} /> */}
          {/* <euroland-add-instrument instrumentId={selectedTicker.instrumentId}></euroland-add-instrument> */}
          <ChartShowRange />
        </div>
      </div>
    </div>
  );
};

export default ChartHeader;
